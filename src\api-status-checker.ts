/**
 * API Status Checker Module
 * 
 * This module provides functionality to check the status of API keys
 * for various AI services by making test requests and returning HTTP status codes.
 * 
 * @module ApiStatusChecker
 */

import { promisifiedRequest } from "./promisifiedRequest";
import { debugLog } from "./debuglog";
import type { ApiServiceName } from "./types";

/**
 * Interface for API status check result
 */
export interface IApiStatusResult {
    readonly statusCode: number;
    readonly isValid: boolean;
    readonly errorMessage?: string;
}

/**
 * API Status Checker class that provides methods to validate API keys
 * for different AI services and return their current status.
 */
export class ApiStatusChecker {
    /**
     * 检查API密钥的状态
     * 
     * @param apiKey - 要检查的API密钥
     * @param serviceName - AI服务名称
     * @returns Promise<IApiStatusResult> - 包含状态码和验证结果的对象
     */
    static async checkApiKeyStatus(apiKey: string, serviceName: ApiServiceName): Promise<IApiStatusResult> {
        try {
            switch (serviceName) {
                case "Gemini":
                    return await this.checkGeminiApiKey(apiKey);
                case "OpenAI":
                    return await this.checkOpenAiApiKey(apiKey);
                case "Claude":
                    return await this.checkClaudeApiKey(apiKey);
                case "Grok":
                    return await this.checkGrokApiKey(apiKey);
                default:
                    debugLog(`不支持的服务类型: ${serviceName}`, "error");
                    return { statusCode: 400, isValid: false, errorMessage: "不支持的服务类型" };
            }
        } catch (error) {
            debugLog(`检查API密钥状态时发生错误: ${error}`, "error");
            return { statusCode: 500, isValid: false, errorMessage: String(error) };
        }
    }

    /**
     * 检查Gemini API密钥状态
     * 基于现有的test_valid.sh脚本，但返回HTTP状态码而不是对话数据
     * 
     * @param apiKey - Gemini API密钥
     * @returns Promise<IApiStatusResult> - 状态检查结果
     */
    private static async checkGeminiApiKey(apiKey: string): Promise<IApiStatusResult> {
        try {
            const url = `https://api-proxy.me/gemini/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;
            
            const response = await promisifiedRequest({
                method: "POST",
                url: url,
                timeout: 10000,
                headers: {
                    "Content-Type": "application/json"
                },
                data: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: "Test"
                        }]
                    }]
                })
            });

            debugLog(`Gemini API状态检查响应: ${response.status}`, "general");
            
            // 根据HTTP状态码判断密钥有效性
            const isValid = response.status >= 200 && response.status < 300;
            return {
                statusCode: response.status,
                isValid: isValid,
                errorMessage: isValid ? undefined : response.statusText
            };

        } catch (error) {
            debugLog(`Gemini API密钥检查失败: ${error}`, "error");
            return { statusCode: 500, isValid: false, errorMessage: String(error) };
        }
    }

    /**
     * 检查OpenAI API密钥状态
     * 
     * @param apiKey - OpenAI API密钥
     * @returns Promise<IApiStatusResult> - 状态检查结果
     */
    private static async checkOpenAiApiKey(apiKey: string): Promise<IApiStatusResult> {
        try {
            const url = "https://api.openai.com/v1/models";
            
            const response = await promisifiedRequest({
                method: "GET",
                url: url,
                timeout: 10000,
                headers: {
                    "Authorization": `Bearer ${apiKey}`,
                    "Content-Type": "application/json"
                }
            });

            debugLog(`OpenAI API状态检查响应: ${response.status}`, "general");
            
            const isValid = response.status >= 200 && response.status < 300;
            return {
                statusCode: response.status,
                isValid: isValid,
                errorMessage: isValid ? undefined : response.statusText
            };

        } catch (error) {
            debugLog(`OpenAI API密钥检查失败: ${error}`, "error");
            return { statusCode: 500, isValid: false, errorMessage: String(error) };
        }
    }

    /**
     * 检查Claude API密钥状态
     * 
     * @param apiKey - Claude API密钥
     * @returns Promise<IApiStatusResult> - 状态检查结果
     */
    private static async checkClaudeApiKey(apiKey: string): Promise<IApiStatusResult> {
        try {
            const url = "https://api.anthropic.com/v1/messages";
            
            const response = await promisifiedRequest({
                method: "POST",
                url: url,
                timeout: 10000,
                headers: {
                    "x-api-key": apiKey,
                    "Content-Type": "application/json",
                    "anthropic-version": "2023-06-01"
                },
                data: JSON.stringify({
                    model: "claude-3-haiku-20240307",
                    max_tokens: 1,
                    messages: [{
                        role: "user",
                        content: "Test"
                    }]
                })
            });

            debugLog(`Claude API状态检查响应: ${response.status}`, "general");
            
            const isValid = response.status >= 200 && response.status < 300;
            return {
                statusCode: response.status,
                isValid: isValid,
                errorMessage: isValid ? undefined : response.statusText
            };

        } catch (error) {
            debugLog(`Claude API密钥检查失败: ${error}`, "error");
            return { statusCode: 500, isValid: false, errorMessage: String(error) };
        }
    }

    /**
     * 检查Grok API密钥状态
     * 
     * @param apiKey - Grok API密钥
     * @returns Promise<IApiStatusResult> - 状态检查结果
     */
    private static async checkGrokApiKey(apiKey: string): Promise<IApiStatusResult> {
        try {
            // 注意：这里使用的是假设的Grok API端点，实际端点可能不同
            const url = "https://api.x.ai/v1/chat/completions";
            
            const response = await promisifiedRequest({
                method: "POST",
                url: url,
                timeout: 10000,
                headers: {
                    "Authorization": `Bearer ${apiKey}`,
                    "Content-Type": "application/json"
                },
                data: JSON.stringify({
                    model: "grok-beta",
                    messages: [{
                        role: "user",
                        content: "Test"
                    }],
                    max_tokens: 1
                })
            });

            debugLog(`Grok API状态检查响应: ${response.status}`, "general");
            
            const isValid = response.status >= 200 && response.status < 300;
            return {
                statusCode: response.status,
                isValid: isValid,
                errorMessage: isValid ? undefined : response.statusText
            };

        } catch (error) {
            debugLog(`Grok API密钥检查失败: ${error}`, "error");
            return { statusCode: 500, isValid: false, errorMessage: String(error) };
        }
    }

    /**
     * 批量检查多个API密钥的状态
     * 
     * @param apiKeys - 要检查的API密钥数组
     * @param serviceName - AI服务名称
     * @returns Promise<Map<string, IApiStatusResult>> - 密钥到状态结果的映射
     */
    static async checkMultipleApiKeys(
        apiKeys: string[], 
        serviceName: ApiServiceName
    ): Promise<Map<string, IApiStatusResult>> {
        const results = new Map<string, IApiStatusResult>();
        
        // 并发检查所有密钥，但限制并发数量以避免过多请求
        const batchSize = 3;
        for (let i = 0; i < apiKeys.length; i += batchSize) {
            const batch = apiKeys.slice(i, i + batchSize);
            const batchPromises = batch.map(async (apiKey) => {
                const result = await this.checkApiKeyStatus(apiKey, serviceName);
                return { apiKey, result };
            });
            
            const batchResults = await Promise.all(batchPromises);
            batchResults.forEach(({ apiKey, result }) => {
                results.set(apiKey, result);
            });
            
            // 在批次之间添加短暂延迟以避免速率限制
            if (i + batchSize < apiKeys.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        return results;
    }
}

export default ApiStatusChecker;
