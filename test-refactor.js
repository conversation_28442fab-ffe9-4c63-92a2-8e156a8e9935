/**
 * 测试重构后的API密钥存储格式
 * 这个脚本用于验证新的字典格式是否正确工作
 */

// 模拟GM_getValue和GM_setValue函数
const mockStorage = {};

function GM_getValue(key, defaultValue) {
    return mockStorage[key] !== undefined ? mockStorage[key] : defaultValue;
}

function GM_setValue(key, value) {
    mockStorage[key] = value;
    console.log(`存储 ${key}:`, value);
}

// 测试旧格式数据
console.log("=== 测试数据迁移 ===");

// 设置一些旧格式的测试数据
GM_setValue("openaiApiKeys", ["sk-test1", "sk-test2"]);
GM_setValue("geminiApiKeys", ["AIzaSy_test1", "AIzaSy_test2"]);

console.log("旧格式数据:");
console.log("OpenAI keys:", GM_getValue("openaiApiKeys", []));
console.log("Gemini keys:", GM_getValue("geminiApiKeys", []));

// 模拟迁移过程
function migrateToNewFormat() {
    const services = ["OpenAI", "Gemini"];
    
    services.forEach(serviceName => {
        const storageKey = `${serviceName.charAt(0).toLowerCase()}${serviceName.slice(1)}ApiKeys`;
        const oldKeys = GM_getValue(storageKey, []);
        
        if (Array.isArray(oldKeys) && oldKeys.length > 0) {
            const newFormat = {};
            oldKeys.forEach(key => {
                // 模拟状态检查，随机分配状态码
                const statusCodes = [200, 401, 429];
                const randomStatus = statusCodes[Math.floor(Math.random() * statusCodes.length)];
                newFormat[key] = randomStatus;
            });
            
            GM_setValue(storageKey, newFormat);
            console.log(`迁移 ${serviceName} 完成`);
        }
    });
}

// 执行迁移
migrateToNewFormat();

console.log("\n=== 迁移后的新格式数据 ===");
console.log("OpenAI keys:", GM_getValue("openaiApiKeys", {}));
console.log("Gemini keys:", GM_getValue("geminiApiKeys", {}));

// 测试新格式的操作
console.log("\n=== 测试新格式操作 ===");

function addNewKey(serviceName, apiKey, statusCode) {
    const storageKey = `${serviceName.charAt(0).toLowerCase()}${serviceName.slice(1)}ApiKeys`;
    const currentKeys = GM_getValue(storageKey, {});
    currentKeys[apiKey] = statusCode;
    GM_setValue(storageKey, currentKeys);
    console.log(`添加新密钥到 ${serviceName}: ${apiKey} (状态: ${statusCode})`);
}

function getKeysByStatus(serviceName, statusCode) {
    const storageKey = `${serviceName.charAt(0).toLowerCase()}${serviceName.slice(1)}ApiKeys`;
    const keys = GM_getValue(storageKey, {});
    const filteredKeys = Object.entries(keys)
        .filter(([key, status]) => status === statusCode)
        .map(([key, status]) => key);
    return filteredKeys;
}

function getStatusCounts(serviceName) {
    const storageKey = `${serviceName.charAt(0).toLowerCase()}${serviceName.slice(1)}ApiKeys`;
    const keys = GM_getValue(storageKey, {});
    const statusCounts = {};
    
    Object.values(keys).forEach(statusCode => {
        statusCounts[statusCode] = (statusCounts[statusCode] || 0) + 1;
    });
    
    return statusCounts;
}

// 测试添加新密钥
addNewKey("OpenAI", "sk-new-test", 200);
addNewKey("Gemini", "AIzaSy_new_test", 401);

// 测试按状态筛选
console.log("\n=== 按状态筛选密钥 ===");
console.log("OpenAI 有效密钥 (200):", getKeysByStatus("OpenAI", 200));
console.log("OpenAI 无效密钥 (401):", getKeysByStatus("OpenAI", 401));
console.log("Gemini 有效密钥 (200):", getKeysByStatus("Gemini", 200));
console.log("Gemini 无效密钥 (401):", getKeysByStatus("Gemini", 401));

// 测试状态统计
console.log("\n=== 状态统计 ===");
console.log("OpenAI 状态统计:", getStatusCounts("OpenAI"));
console.log("Gemini 状态统计:", getStatusCounts("Gemini"));

// 测试UI显示格式
console.log("\n=== UI显示格式测试 ===");

function formatForUI() {
    const services = ["OpenAI", "Gemini"];
    let totalKeys = 0;
    
    services.forEach(serviceName => {
        const storageKey = `${serviceName.charAt(0).toLowerCase()}${serviceName.slice(1)}ApiKeys`;
        const keys = GM_getValue(storageKey, {});
        const keyCount = Object.keys(keys).length;
        totalKeys += keyCount;
        
        if (keyCount > 0) {
            const statusCounts = getStatusCounts(serviceName);
            const statusParts = [];
            
            Object.entries(statusCounts).forEach(([status, count]) => {
                const statusCode = parseInt(status);
                let statusText = "";
                if (statusCode >= 200 && statusCode < 300) {
                    statusText = "有效";
                } else if (statusCode === 401 || statusCode === 403) {
                    statusText = "无效";
                } else if (statusCode === 429) {
                    statusText = "未知";
                } else {
                    statusText = `状态${statusCode}`;
                }
                statusParts.push(`${statusText}: ${count}`);
            });
            
            console.log(`${serviceName}: ${keyCount} 个 (${statusParts.join(", ")})`);
        }
    });
    
    console.log(`总计: ${totalKeys} 个密钥`);
}

formatForUI();

console.log("\n=== 测试完成 ===");
console.log("重构验证成功！新的字典格式正常工作。");
