/**
 * UI Creators Module
 *
 * This module provides static methods for creating UI elements for the
 * GitHub API Key Finder interface. All methods follow a consistent pattern
 * of creating DOM elements, applying styles, and setting up event handlers.
 *
 * @module UICreators
 */

import PANEL_STYLES from '../styles';
import { AI_SERVICES } from "../constants";
import { UIElements } from "./ui-elements";
import { UIHandlers } from './ui-handlers';

/**
 * Static utility class for creating UI elements.
 *
 * This class provides factory methods for creating various UI components
 * used throughout the GitHub API Key Finder interface. All methods are
 * static and follow consistent naming conventions and patterns.
 *
 * @class UICreators
 *
 * @example
 * ```typescript
 * // Apply styles to the document
 * UICreators.applyStyles();
 *
 * // Create a button
 * const button = UICreators.createButton("Click me", () => console.log("Clicked"));
 *
 * // Create the main control panel
 * const panel = UICreators.createControlPanel();
 * ```
 */
export class UICreators {
    /**
     * Applies CSS styles for the control panel to the document head.
     *
     * This method injects the required CSS styles into the document head
     * to ensure proper styling of the GitHub API Key Finder interface.
     * Should be called once during initialization.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * UICreators.applyStyles();
     * ```
     */
    static applyStyles = (): void => {
        const style: HTMLStyleElement = document.createElement("style");
        style.textContent = PANEL_STYLES;
        document.head.appendChild(style);
    }

    /**
     * Creates and returns a button element with specified text and click handler.
     *
     * This is a utility method for creating standardized button elements
     * throughout the interface. The button will have the specified text
     * and will call the provided handler when clicked.
     *
     * @static
     * @param text - The text to display on the button
     * @param onClickHandler - The function to call when the button is clicked
     * @param className - Optional CSS class name to apply to the button
     * @returns The created button element
     *
     * @example
     * ```typescript
     * const saveButton = UICreators.createButton(
     *   "Save",
     *   () => console.log("Saving..."),
     *   "save-button"
     * );
     * document.body.appendChild(saveButton);
     * ```
     */
    static createButton = (text: string, onClickHandler: () => void, className?: string): HTMLButtonElement => {
        const button: HTMLButtonElement = document.createElement("button");
        button.textContent = text;
        button.addEventListener("click", onClickHandler);
        if (className) {
            button.className = className;
        }
        return button;
    }

    /**
     * Creates the API type selection dropdown with label and options.
     *
     * This method creates a complete dropdown interface for selecting
     * which type of API keys to search for. It includes options for
     * all supported AI services plus an "All Types" option.
     *
     * @static
     * @returns The div container containing the label and select element
     *
     * @example
     * ```typescript
     * const dropdown = UICreators.createApiKeyTypeSelect();
     * document.body.appendChild(dropdown);
     * ```
     */
    static createApiKeyTypeSelect = (): HTMLDivElement => {
        const selectDiv: HTMLDivElement = document.createElement("div");
        selectDiv.style.marginBottom = "10px";
        selectDiv.style.textAlign = "center";

        const label: HTMLLabelElement = document.createElement("label");
        label.textContent = "选择API类型: ";
        label.htmlFor = "apiKeyTypeSelect";
        selectDiv.appendChild(label);

        const selectElement: HTMLSelectElement = document.createElement("select");
        UIElements.apiKeyTypeSelectElement = selectElement;
        selectElement.id = "apiKeyTypeSelect";
        selectElement.style.padding = "5px";
        selectElement.style.borderRadius = "3px";

        // Add "All Types" option
        const allOption: HTMLOptionElement = document.createElement("option");
        allOption.value = "ALL";
        allOption.textContent = "全部类型";
        selectElement.appendChild(allOption);

        // Add options for each AI service
        AI_SERVICES.forEach((service: { name: string }) => {
            const option: HTMLOptionElement = document.createElement("option");
            option.value = service.name;
            option.textContent = service.name;
            selectElement.appendChild(option);
        });

        // Set default selection to "Gemini"
        selectElement.value = "Gemini";
        selectDiv.appendChild(selectElement);
        return selectDiv;
    }

    /**
     * Creates and returns the panel title element.
     *
     * This method creates the main heading for the control panel
     * with the application title.
     *
     * @static
     * @returns The heading element containing the panel title
     *
     * @example
     * ```typescript
     * const title = UICreators.createPanelTitle();
     * panel.appendChild(title);
     * ```
     */
    static createPanelTitle = (): HTMLHeadingElement => {
        const title: HTMLHeadingElement = document.createElement("h3");
        title.textContent = "GitHub API 密钥查找器";
        return title;
    }

    /**
     * Creates and returns the button to toggle panel content visibility.
     *
     * This method creates a toggle button that allows users to show/hide
     * the panel content. The button text changes based on the current
     * visibility state and the button reference is stored in UIElements.
     *
     * @static
     * @returns The toggle button element
     *
     * @example
     * ```typescript
     * const toggleButton = UICreators.createPanelToggleButton();
     * panel.appendChild(toggleButton);
     * ```
     */
    static createPanelToggleButton = (): HTMLButtonElement => {
        const button: HTMLButtonElement = UICreators.createButton(
            UIElements.panelContentVisible ? "隐藏面板" : "显示面板",
            UIHandlers.handleTogglePanelContent,
            "toggle-panel-button"
        );
        UIElements.togglePanelContentButton = button;
        button.style.width = "100%";
        button.style.marginBottom = "10px";
        return button;
    }

    /**
     * Creates and returns the API key display area element.
     *
     * This method creates a div element that will be used to display
     * found API keys and statistics. The element reference is stored
     * in UIElements for later access.
     *
     * @static
     * @returns The div element for displaying API keys
     *
     * @example
     * ```typescript
     * const displayArea = UICreators.createKeyDisplayArea();
     * panel.appendChild(displayArea);
     * ```
     */
    static createKeyDisplayArea = (): HTMLDivElement => {
        const div: HTMLDivElement = document.createElement("div");
        UIElements.keysDisplayElement = div;
        div.id = "apiKeyDisplayArea";
        div.classList.add("panel-content-toggleable");
        div.dataset.initialDisplay = "block";
        return div;
    }

    /**
     * Creates and returns the container for the API key type selection dropdown.
     *
     * This method creates a wrapper for the API key type selection dropdown
     * and configures it for panel content toggling. It reuses the
     * createApiKeyTypeSelect method and stores the container reference.
     *
     * @static
     * @returns The container div for the API key type selection
     *
     * @example
     * ```typescript
     * const selectArea = UICreators.createApiKeySelectArea();
     * panel.appendChild(selectArea);
     * ```
     */
    static createApiKeySelectArea = (): HTMLDivElement => {
        const container: HTMLDivElement = UICreators.createApiKeyTypeSelect();
        UIElements.apiKeyTypeSelectContainer = container;
        container.classList.add("panel-content-toggleable");
        container.dataset.initialDisplay = "block";
        return container;
    }

    /**
     * Creates and returns the actions button area.
     *
     * This method creates a container with all the action buttons:
     * search, copy keys, clear keys, and copy debug info. The search
     * button reference is stored in UIElements for later access.
     *
     * @static
     * @returns The div container with all action buttons
     *
     * @example
     * ```typescript
     * const actionsArea = UICreators.createActionsArea();
     * panel.appendChild(actionsArea);
     * ```
     */
    static createActionsArea = (): HTMLDivElement => {
        const actionsDiv: HTMLDivElement = document.createElement("div");
        actionsDiv.className = "actions panel-content-toggleable";
        actionsDiv.dataset.initialDisplay = "flex";
        actionsDiv.style.textAlign = "center";

        const searchButton: HTMLButtonElement = UICreators.createButton("搜索", UIHandlers.handleRunSearch, undefined);
        UIElements.searchButtonElement = searchButton;
        actionsDiv.appendChild(searchButton);

        const testKeysButton: HTMLButtonElement = UICreators.createButton("测试密钥", UIHandlers.handleTestKeys, undefined);
        UIElements.testKeysButtonElement = testKeysButton;
        actionsDiv.appendChild(testKeysButton);

        actionsDiv.appendChild(UICreators.createButton("复制", UIHandlers.handleCopyKeys, undefined));
        actionsDiv.appendChild(UICreators.createButton("清空", UIHandlers.handleClearKeys, "clear"));
        actionsDiv.appendChild(UICreators.createButton("调试", UIHandlers.handleCopyDebugInfo, undefined));
        return actionsDiv;
    }

    /**
     * Creates and returns the progress display element.
     *
     * This method creates a div element for displaying search progress
     * information. The element reference is stored in UIElements and
     * initialized with a "ready" message.
     *
     * @static
     * @returns The div element for displaying progress information
     *
     * @example
     * ```typescript
     * const progressArea = UICreators.createProgressArea();
     * panel.appendChild(progressArea);
     * ```
     */
    static createProgressArea = (): HTMLDivElement => {
        const div: HTMLDivElement = document.createElement("div");
        UIElements.progressElement = div;
        div.id = "apiKeyFinderProgress";
        div.textContent = "准备就绪";
        div.classList.add("panel-content-toggleable");
        div.dataset.initialDisplay = "block";
        return div;
    }

    /**
     * Creates and returns the status display element.
     *
     * This method creates a div element for displaying status information
     * such as the number of stored keys and search statistics. The element
     * reference is stored in UIElements for later updates.
     *
     * @static
     * @returns The div element for displaying status information
     *
     * @example
     * ```typescript
     * const statusArea = UICreators.createStatusArea();
     * panel.appendChild(statusArea);
     * ```
     */
    static createStatusArea = (): HTMLDivElement => {
        const div: HTMLDivElement = document.createElement("div");
        UIElements.statusElement = div;
        div.id = "apiKeyFinderStatus";
        div.classList.add("panel-content-toggleable");
        div.dataset.initialDisplay = "block";
        return div;
    }
}
