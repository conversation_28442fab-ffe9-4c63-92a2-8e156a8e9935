import { debugLog } from "../debuglog";
import { promisifiedRequest } from "../promisifiedRequest";
import { SEARCH_FILE_TYPES, GITHUB_SEARCH_SECTION, AI_SERVICES } from "../constants";
import { UI } from "../ui";
import { processSearchResultItem } from "./search-core";
import GitHubSearchUrlBuilder from "./search-url-builder";
import { debugLog as IDebugLog } from "../debuglog";
import GitHubSearchState from "./search-state";
import { SearchLogic } from "../searchLogic";
import type { IStoredKeys, IApiService } from "../types";

export const searchState = new GitHubSearchState();
const searchUrlBuilder = new GitHubSearchUrlBuilder();

/**
 * Performs a search for a given service in a specific section and page.
 * Extracts raw file URLs from search results and fetches their content.
 * @param {object} service - The AI service object.
 * @param {string} section - The search section.
 * @param {number} page - The page number.
 * @returns {Promise<boolean>} Resolves with true if search should continue, false if stopped or error.
 */
export async function performSingleSearch(service: IApiService, fileType: string, section: string, page = 1): Promise<boolean> {
    if (searchState.getStopSearchFlag()) {
        IDebugLog(
            `停止标志已设置，跳过搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})`,
            "general"
        );
        return false; // Indicate that search should stop
    }

    debugLog(
        `开始搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})`,
        "general"
    );
    const searchUrl = searchUrlBuilder.constructSearchURL(
        service.keywords,
        fileType,
        section,
        page
    );
    IDebugLog(`搜索 URL: ${searchUrl}`, "general");

    try {
        const response = await promisifiedRequest({
            method: "GET",
            url: searchUrl,
            // No timeout specified in original GM_xmlhttpRequest for search page
        });

        // Check stop flag after successful request but before processing
        if (searchState.getStopSearchFlag()) {
            IDebugLog(
                `停止标志已设置，获取成功但跳过处理搜索结果页: ${searchUrl}`,
                "general"
            );
            return false; // Indicate that search should stop
        }

        if (response.status >= 200 && response.status < 300) {
            const htmlContent = response.responseText;
            debugLog(
                `GitHub 搜索结果 (前200字符): ${htmlContent.substring(0, 200)}...`,
                "general"
            );

            let embeddedData = null;
            try {
                const parser = new DOMParser();
                const doc = parser.parseFromString(htmlContent, "text/html");
                const scriptElement = doc.querySelector(
                    'script[type="application/json"][data-target="react-app.embeddedData"]'
                );
                if (scriptElement) {
                    embeddedData = JSON.parse(scriptElement.textContent || "{}");
                    debugLog(
                        {
                            message: "成功提取并解析 embeddedData JSON",
                            data: embeddedData,
                        },
                        "jsonExtract"
                    );
                } else {
                    debugLog(
                        '未找到 <script data-target="react-app.embeddedData">。',
                        "error"
                    );
                }
            } catch (e: any) {
                debugLog(`解析 embeddedData JSON 失败: ${e.message}`, "error");
            }

            if (embeddedData?.payload?.results) {
                debugLog(
                    `从 embeddedData 找到 ${embeddedData.payload.results.length} 个结果。`,
                    "general"
                );
                // Process each search result item
                for (const item of embeddedData.payload.results) {
                    // IMPORTANT: This is the request delay logic.
                    await new Promise((resolveDelay) =>
                        setTimeout(resolveDelay, 2000 + Math.random() * 2000)
                    );

                    // Check stop flag before processing the next item
                    if (searchState.getStopSearchFlag()) {
                        IDebugLog(
                            `停止标志已设置，跳过处理搜索结果项: ${item.path}`,
                            "general"
                        );
                        break; // Exit the loop early
                    }
                    // Increment total files to process BEFORE processing the item
                    searchState.setTotalFilesToProcess(searchState.getTotalFilesToProcess() + 1);
                    await processSearchResultItem(item, service.name);
                }
            } else {
                IDebugLog(
                    "embeddedData 中未找到 payload.results 或结构不符。",
                    "general"
                );
            }

            // After processing all items in this page's results
            if (!searchState.getStopSearchFlag() && embeddedData?.payload?.results?.length > 0) {
                IDebugLog(`当前页 (${page}) 的所有搜索结果项处理完成。`, "general");
                // The totalFilesToProcess and processedFilesCount are updated within processSearchResultItem/fetchAndProcessRawFile
            } else if (
                !searchState.getStopSearchFlag() &&
                embeddedData?.payload?.results?.length === 0
            ) {
                IDebugLog(`当前页 (${page}) 未找到搜索结果项。`, "general");
            } else if (searchState.getStopSearchFlag()) {
                IDebugLog(
                    `因停止标志设置，当前页 (${page}) 的搜索结果处理被中断。`,
                    "general"
                );
            }
            return !searchState.getStopSearchFlag(); // Resolve with false if stop flag is set
        } else {
            debugLog(
                `获取 GitHub 搜索页失败. 状态: ${response.status}, URL: ${searchUrl}`,
                "error"
            );
            return false; // Indicate search page fetch failure
        }
    } catch (error: any) {
        // Handle network errors from promisifiedRequest
        if (searchState.getStopSearchFlag()) {
            IDebugLog(
                `停止标志已设置，获取 GitHub 搜索页时中断. URL: ${searchUrl}`,
                "general"
            );
        } else {
            IDebugLog(
                `获取 GitHub 搜索页时网络错误. URL: ${searchUrl}, 错误: ${error.statusText || error.message || "未知"
                }`,
                "error"
            );
        }
        return false; // Indicate failure or stop
    }
}

/**
 * Main logic for the API key search.
 * @param {string} selectedApiKeyType - The type of API key to search for ("ALL" or specific service name).
 */
export async function mainSearchLogic(selectedApiKeyType = "ALL"): Promise<void> {
    IDebugLog(`开始主搜索逻辑，密钥类型: ${selectedApiKeyType}`, "general");

    // Load stored API keys to exclude them from the search
    const storedKeys: IStoredKeys = SearchLogic.getStoredCategorizedKeys() as IStoredKeys;
    IDebugLog({ message: "从存储加载密钥", data: storedKeys }, "general");

    // Iterate through each AI service and search for API keys
    for (const service of Object.values(AI_SERVICES) as { name: "OpenAI" | "Gemini" | "Grok" | "Claude"; keywords: string[]; }[]) {
        if (selectedApiKeyType !== "ALL" && service.name !== selectedApiKeyType) {
            IDebugLog(`跳过 ${service.name}，因为它与所选类型不匹配。`, "general");
            continue; // Skip if not "ALL" and doesn't match the selected type
        }

        // Exclude already stored keys from the search query
        if (storedKeys && storedKeys[service.name]) {
            Object.keys(storedKeys[service.name]).forEach((key: string) => {
                const escapedKey = key.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // Escape special regex characters
                if (service && service.keywords) {
                    service.keywords = service.keywords.filter((keyword: string) => !keyword.includes(escapedKey));
                }
            });
        }

        // Iterate through each file type
        for (const fileType of SEARCH_FILE_TYPES) {
            if (searchState.getStopSearchFlag()) {
                IDebugLog("停止标志已设置，跳过文件类型搜索。", "general");
                break; // Exit file type loop
            }
            // Loop through pages 1 to 5
            for (let page = 1; page <= 5; page++) {
                if (searchState.getStopSearchFlag()) {
                    IDebugLog("停止标志已设置，跳过页面搜索。", "general");
                    break; // Exit page loop
                }

                try {
                    // IMPORTANT: This is the request delay logic.
                    await new Promise((resolveDelay) => {
                        if (searchState.getStopSearchFlag()) {
                            IDebugLog("停止标志已设置，跳过延迟。", "general");
                            resolveDelay(undefined); // Resolve immediately if stopping
                        } else {
                            setTimeout(resolveDelay, 2000 + Math.random() * 2000); // Calculate random delay here
                        }
                    });

                    if (searchState.getStopSearchFlag()) {
                        IDebugLog(
                            "停止标志已设置，跳过 performSingleSearch。",
                            "general"
                        );
                        break; // Exit page loop
                    }

                    const success = await performSingleSearch(
                        service,
                        fileType,
                        GITHUB_SEARCH_SECTION,
                        page
                    );
                    if (!success) {
                        IDebugLog(
                            `搜索 ${service.name} ${fileType} 在 ${GITHUB_SEARCH_SECTION} (页 ${page}) 失败或已停止，停止当前页循环。`,
                            "general"
                        );
                        break; // Stop the current page loop
                    }
                } catch (error: any) {
                    if (searchState.getStopSearchFlag()) {
                        IDebugLog(
                            `停止标志已设置，搜索 ${service.name} ${fileType} 在 ${GITHUB_SEARCH_SECTION} (页 ${page}) 时中断。`,
                            "general"
                        );
                    } else {
                        IDebugLog(
                            `搜索 ${service.name} ${fileType} 在 ${GITHUB_SEARCH_SECTION} (页 ${page}) 时发生未捕获错误: ${error.message || error}`,
                            "error"
                        );
                    }
                    // Even if there's an unexpected error here, we should probably stop the page loop
                    break;
                }
            } // End page loop
        } // End file type loop
        if (searchState.getStopSearchFlag()) {
            IDebugLog(`检测到停止标志，跳过 ${service.name} 的其余部分。`, "general");
            break; // Break out of the service loop
        }
    }

    IDebugLog("主搜索逻辑完成。", "general");
    UI.updateProgressDisplay("搜索完成");
}