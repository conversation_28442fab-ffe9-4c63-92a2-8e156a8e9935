import { GM_setValue, GM_registerMenuCommand } from '$';
import { debugLog } from "./debuglog";
import { UI } from "./ui";
import { SearchLockManager } from "./searchLogic/search-lock-manager";

(function () {
  "use strict";

  // 创建全局搜索锁定管理器实例
  const searchLockManager = new SearchLockManager();

  // 将锁定管理器暴露给全局作用域，供其他模块使用
  (window as any).searchLockManager = searchLockManager;

  // --- Initialization ---
  function initialize() {
    // 检查是否有其他页面正在搜索
    const lockInfo = searchLockManager.getLockInfo();

    if (lockInfo.isLocked && lockInfo.searchInProgress) {
      // 有其他页面正在搜索，不清空newlyFoundApiKeys
      debugLog(`[+] 检测到其他页面正在搜索 (${lockInfo.lockedBy})，保留现有数据。`, "general");
    } else {
      // 没有其他页面在搜索，或者锁定已过期，清空数据
      if (lockInfo.isLocked && !lockInfo.searchInProgress) {
        debugLog(`[+] 检测到过期的搜索锁定，清理并重置数据。`, "general");
        searchLockManager.forceReleaseLock();
      }

      // Clear newly found keys from the previous session at the start of the script
      GM_setValue("newlyFoundApiKeys", {});
      debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");
    }

    UI.applyStyles();
    const controlPanelElement = UI.createControlPanel(); // This now also sets up panelContentElements
    document.body.appendChild(controlPanelElement);
    GM_registerMenuCommand("切换 API 密钥查找器面板", UI.toggleControlPanel);

    // 使用新的UI状态更新方法，包括锁定状态检查
    UI.updateUIState(); // Initial display of keys and button state

    // 设置定期检查锁定状态的定时器
    setInterval(() => {
      UI.updateSearchButtonState();
    }, 5000); // 每5秒检查一次锁定状态

    // Set initial panel content visibility
    // UI.panelContentVisible = false; // Default to hide content - This state is now managed within the UI module
    // UI.updatePanelContentVisibility(); // Apply the visibility state

    debugLog("API 密钥查找器 UI 已初始化。", "general");
  }

  // Ensure script runs after the DOM is fully loaded
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    initialize();
  } else {
    window.addEventListener("DOMContentLoaded", initialize);
  }
})();
