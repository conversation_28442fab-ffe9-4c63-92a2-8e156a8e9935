/**
 * UI Module Index
 *
 * This module serves as the main entry point for the UI system, providing
 * a unified interface that combines all UI functionality from sub-modules.
 * It acts as a facade pattern implementation for the UI components.
 *
 * @module UI
 */

import { UIElements } from "./ui-elements";
import { UICreators } from "./ui-creators";
import { UIHandlers } from "./ui-handlers";
import { UIUpdaters } from "./ui-updaters";

/**
 * Main UI class that provides a unified interface for all UI operations.
 *
 * This class acts as a facade that combines functionality from all UI sub-modules
 * (creators, handlers, updaters, elements) into a single, easy-to-use interface.
 * All methods are static and delegate to the appropriate sub-module.
 *
 * @class UI
 *
 * @example
 * ```typescript
 * // Apply styles and create the main panel
 * UI.applyStyles();
 * const panel = UI.createControlPanel();
 * document.body.appendChild(panel);
 *
 * // Update display and handle user interactions
 * UI.updateKeyDisplay();
 * await UI.handleRunSearch();
 * ```
 */
export class UI {
    // --- Style Management ---
    static applyStyles = UICreators.applyStyles;

    // --- Display Updates ---
    static updateKeyDisplay = UIUpdaters.updateKeyDisplay;
    static updateProgressDisplay = UIUpdaters.updateProgressDisplay;

    // --- Element Creation ---
    static createButton = UICreators.createButton;
    static createApiKeyTypeSelect = UICreators.createApiKeyTypeSelect;
    static createPanelTitle = UICreators.createPanelTitle;
    static createPanelToggleButton = UICreators.createPanelToggleButton;
    static createKeyDisplayArea = UICreators.createKeyDisplayArea;
    static createApiKeySelectArea = UICreators.createApiKeySelectArea;
    static createActionsArea = UICreators.createActionsArea;
    static createProgressArea = UICreators.createProgressArea;
    static createStatusArea = UICreators.createStatusArea;

    // --- Event Handlers ---
    static handleTogglePanelContent = UIHandlers.handleTogglePanelContent;
    static handleRunSearch = UIHandlers.handleRunSearch;
    static handleCopyKeys = UIHandlers.handleCopyKeys;
    static handleClearKeys = UIHandlers.handleClearKeys;
    static handleCopyDebugInfo = UIHandlers.handleCopyDebugInfo;

    /**
     * Toggles the visibility of the main control panel.
     *
     * This method shows or hides the entire control panel and refreshes
     * the display when the panel becomes visible. It also applies the
     * current panel content visibility state.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Toggle panel visibility
     * UI.toggleControlPanel();
     * ```
     */
    static toggleControlPanel = (): void => {
        UIElements.controlPanelVisible = !UIElements.controlPanelVisible;
        if (UIElements.controlPanelElement) {
            UIElements.controlPanelElement.style.display = UIElements.controlPanelVisible
                ? "flex"
                : "none";
            if (UIElements.controlPanelVisible) {
                UIUpdaters.updateKeyDisplay(); // Refresh display when shown
                // Apply current panel content visibility state
                UIUpdaters.updatePanelContentVisibility();
            }
        }
    }

    /**
     * Creates the main control panel UI.
     *
     * This method orchestrates the creation of all panel sub-elements and
     * assembles them into a complete control panel. It sets up the panel
     * structure and stores the reference in UIElements for later access.
     *
     * @static
     * @returns The main panel element containing all UI components
     *
     * @example
     * ```typescript
     * // Create and add the control panel to the page
     * const panel = UI.createControlPanel();
     * document.body.appendChild(panel);
     * ```
     */
    static createControlPanel = (): HTMLDivElement => {
        const panel: HTMLDivElement = document.createElement("div");
        panel.id = "apiKeyFinderPanel";

        // Store reference for later access
        UIElements.controlPanelElement = panel;

        // Add all panel components in order
        panel.appendChild(UICreators.createPanelTitle());
        panel.appendChild(UICreators.createPanelToggleButton());
        panel.appendChild(UICreators.createKeyDisplayArea());
        panel.appendChild(UICreators.createApiKeySelectArea());
        panel.appendChild(UICreators.createActionsArea());
        panel.appendChild(UICreators.createProgressArea());
        panel.appendChild(UICreators.createStatusArea());

        // Initial visibility will be set in initialize() and updatePanelContentVisibility

        return panel;
    }
}

/**
 * Default export for the UI class.
 *
 * This allows importing the UI class using either named or default import syntax.
 *
 * @example
 * ```typescript
 * // Named import
 * import { UI } from './ui';
 *
 * // Default import
 * import UI from './ui';
 * ```
 */
export default UI;
