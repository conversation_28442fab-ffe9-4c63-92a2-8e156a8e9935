import { API_KEY_PATTERNS } from "../constants";
import type { ApiServiceName, IKeyExtractor } from "../types";

/**
 * API Key Extraction Module
 *
 * This module provides functionality to extract API keys from text content
 * using predefined regular expression patterns for different AI services.
 *
 * @module KeyExtraction
 */

/**
 * Extracts API keys from text content using pattern matching.
 *
 * The ApiKeyExtractor class implements the IKeyExtractor interface and provides
 * methods to identify and extract potential API keys from raw text content
 * based on service-specific regular expression patterns.
 *
 * @class ApiKeyExtractor
 * @implements {IKeyExtractor}
 *
 * @example
 * ```typescript
 * const extractor = new ApiKeyExtractor();
 * const keys = extractor.extractPotentialKeys(fileContent, "OpenAI");
 * console.log(`Found ${keys.length} potential OpenAI keys`);
 * ```
 */
export class ApiKeyExtractor implements IKeyExtractor {
    /**
     * Extracts potential API keys from text content based on service-specific patterns.
     *
     * This method uses predefined regular expression patterns to identify potential
     * API keys for the specified service. It ensures no duplicate keys are returned
     * by using a Set internally.
     *
     * @param textContent - The raw text content to search for API keys
     * @param serviceName - The name of the AI service to search keys for
     * @returns An array of unique potential API key strings
     *
     * @example
     * ```typescript
     * const extractor = new ApiKeyExtractor();
     * const content = "const apiKey = 'sk-1234567890abcdef';";
     * const keys = extractor.extractPotentialKeys(content, "OpenAI");
     * // Returns: ["sk-1234567890abcdef"]
     * ```
     *
     * @throws {Error} Does not throw errors, returns empty array if patterns not found
     */
    public extractPotentialKeys(textContent: string, serviceName: ApiServiceName): string[] {
        // Validate input parameters
        if (typeof textContent !== 'string' || textContent.length === 0) {
            return [];
        }

        const patterns: readonly RegExp[] | undefined = API_KEY_PATTERNS[serviceName];
        if (!patterns || patterns.length === 0) {
            return [];
        }

        const foundKeys: Set<string> = new Set<string>();

        patterns.forEach((pattern: RegExp) => {
            // Create a new regex instance to avoid stateful issues with global flag
            const currentRegex: RegExp = new RegExp(pattern.source, pattern.flags);
            let match: RegExpExecArray | null;

            while ((match = currentRegex.exec(textContent)) !== null) {
                const extractedKey: string = match[0];
                if (extractedKey && extractedKey.trim().length > 0) {
                    foundKeys.add(extractedKey.trim());
                }

                // Prevent infinite loops with zero-length matches
                if (match.index === currentRegex.lastIndex) {
                    currentRegex.lastIndex++;
                }
            }
        });

        return Array.from(foundKeys);
    }
}

export default ApiKeyExtractor;