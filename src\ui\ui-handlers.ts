/**
 * UI Event Handlers Module
 *
 * This module provides event handlers for all UI interactions in the
 * GitHub API Key Finder interface. All handlers follow consistent patterns
 * for error handling, user feedback, and state management.
 *
 * @module UIHandlers
 */

import { SearchLogic } from "../searchLogic";
import { mainSearchLogic, searchState } from "../searchLogic/main-search";
import { GM_setValue, GM_getValue, GM_setClipboard } from '$';
import { UIElements } from "./ui-elements";
import { UIUpdaters } from "./ui-updaters";
import { AI_SERVICES } from "../constants";
import { debugLog } from "../debuglog";
import { ApiStatusChecker } from "../api-status-checker";
import type { INewlyFoundKeys, ApiServiceName } from "../types";

/**
 * Static utility class for handling UI events and interactions.
 *
 * This class provides event handlers for all user interactions including
 * button clicks, panel toggles, and other UI events. All methods are static
 * and work with the UIElements singleton to access DOM element references.
 *
 * @class GitHubKeyFinderUIHandlers
 *
 * @example
 * ```typescript
 * // Toggle panel content visibility
 * GitHubKeyFinderUIHandlers.handleTogglePanelContent();
 *
 * // Start/stop search
 * await GitHubKeyFinderUIHandlers.handleRunSearch();
 *
 * // Copy found keys
 * GitHubKeyFinderUIHandlers.handleCopyKeys();
 * ```
 */
export class GitHubKeyFinderUIHandlers {
    /**
     * Toggles the visibility of the main panel content (excluding the toggle button itself).
     *
     * This method toggles the panelContentVisible state and updates the visibility
     * of all panel content elements accordingly. The toggle button itself remains
     * visible to allow users to show the content again.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const toggleButton = document.createElement('button');
     * toggleButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleTogglePanelContent);
     * ```
     */
    static handleTogglePanelContent = (): void => {
        UIElements.panelContentVisible = !UIElements.panelContentVisible;
        UIUpdaters.updatePanelContentVisibility();
    }

    // --- Button Action Handlers ---

    /**
     * Handles the search button click to start or stop the API key search process.
     *
     * This method toggles between starting and stopping the search based on the
     * current button state. When starting a search, it updates the UI to show
     * progress and runs the main search logic. When stopping, it sets the stop
     * flag to gracefully halt the search process.
     *
     * @static
     * @returns Promise that resolves when the search operation is complete
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const searchButton = document.createElement('button');
     * searchButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleRunSearch);
     * ```
     */
    static handleRunSearch = async (): Promise<void> => {
        if (UIElements.searchButtonElement && UIElements.searchButtonElement.textContent === "搜索") {
            // Start search
            UIElements.searchButtonElement.textContent = "停止";
            UIElements.searchButtonElement.style.backgroundColor = "#dc3545"; // Red color
            searchState.setStopSearchFlag(false); // Reset stop flag for a new search

            searchState.resetProgress();
            UIUpdaters.updateProgressDisplay("准备开始搜索...");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "正在准备搜索...";

            const selectedApiKeyType: string = UIElements.apiKeyTypeSelectElement
                ? UIElements.apiKeyTypeSelectElement.value
                : "ALL";
            debugLog(
                `UI: 手动执行搜索... 选择的类型: ${selectedApiKeyType}`,
                "general"
            );

            // Run the main search logic
            await mainSearchLogic(selectedApiKeyType);

            // Search finished (either completed or stopped)
            UIUpdaters.updateKeyDisplay(); // updateKeyDisplay now reads categorized keys

            const finalCategorizedKeys: { [key: string]: { [apiKey: string]: number } } = SearchLogic.getStoredCategorizedKeys();
            let totalKeysCount: number = 0;
            for (const category in finalCategorizedKeys) {
                totalKeysCount += Object.keys(finalCategorizedKeys[category]).length;
            }
            const newlyFoundKeys: INewlyFoundKeys = GM_getValue("newlyFoundApiKeys", {}) as INewlyFoundKeys;
            const newlyFoundCount = Object.keys(newlyFoundKeys).length;
            const finalMsg: string = searchState.getStopSearchFlag()
                ? `搜索已停止！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundCount} 个。`
                : `搜索完成！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundCount} 个。`;

            if (UIElements.statusElement) UIElements.statusElement.textContent = finalMsg;
            UIUpdaters.updateProgressDisplay("所有搜索流程完毕，准备就绪。"); // Final progress update
            alert(
                searchState.getStopSearchFlag()
                    ? "API 密钥搜索已停止！"
                    : "API 密钥搜索完成！"
            );

            // Revert button state after search finishes
            UIElements.searchButtonElement.textContent = "搜索";
            UIElements.searchButtonElement.style.backgroundColor = "#0d6efd"; // Original blue color
        } else if (UIElements.searchButtonElement) {
            // Stop search
            debugLog("UI: 手动停止搜索。", "general");
            searchState.setStopSearchFlag(true); // Signal search to stop
            if (UIElements.statusElement) UIElements.statusElement.textContent = "正在停止搜索...";
            UIUpdaters.updateProgressDisplay("正在停止...");
        }
    }

    /**
     * Copies the newly found API keys from the current session to the clipboard.
     *
     * This method retrieves all API keys found in the current session and copies
     * them to the clipboard with their status codes. It provides user feedback
     * through alerts and status updates.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const copyButton = document.createElement('button');
     * copyButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleCopyKeys);
     * ```
     */
    static handleCopyKeys = (): void => {
        const newlyFoundKeys: INewlyFoundKeys = GM_getValue("newlyFoundApiKeys", {}) as INewlyFoundKeys; // Get keys found in the current session
        const keyCount = Object.keys(newlyFoundKeys).length;

        if (keyCount > 0) {
            // Format keys with status codes in key:statuscode format
            const keyLines: string[] = [];
            Object.entries(newlyFoundKeys).forEach(([key, statusCode]) => {
                keyLines.push(`${key}:${statusCode}`);
            });

            const clipboardContent: string = keyLines.join("\n");
            GM_setClipboard(clipboardContent.trim(), "text");
            alert(`${keyCount} 个本次新发现的密钥（key:statuscode格式）已复制到剪贴板！`);
            if (UIElements.statusElement)
                UIElements.statusElement.textContent = "本次新发现的密钥（key:statuscode格式）已复制到剪贴板。";
        } else {
            alert("本次运行没有发现新的密钥可供复制。");
            if (UIElements.statusElement)
                UIElements.statusElement.textContent = "本次运行没有发现新的密钥可供复制。";
        }
    }

    /**
     * Tests the status of all stored API keys and updates their status codes.
     *
     * This method retrieves all stored API keys from all services, tests their
     * current status by making API calls, and updates the stored status codes.
     * It provides progress feedback and final results to the user.
     *
     * @static
     * @returns Promise that resolves when all keys have been tested
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const testButton = document.createElement('button');
     * testButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleTestKeys);
     * ```
     */
    static handleTestKeys = async (): Promise<void> => {
        if (!confirm("确定要测试所有已存储的API密钥状态吗？这可能需要一些时间。")) {
            return;
        }

        // Disable the test button during testing
        if (UIElements.testKeysButtonElement) {
            UIElements.testKeysButtonElement.textContent = "测试中...";
            (UIElements.testKeysButtonElement as HTMLButtonElement).disabled = true;
        }

        UIUpdaters.updateProgressDisplay("开始测试API密钥状态...");
        if (UIElements.statusElement) {
            UIElements.statusElement.textContent = "正在测试API密钥状态...";
        }

        let totalKeys = 0;
        let testedKeys = 0;
        let updatedKeys = 0;

        try {
            // Get all stored keys
            const categorizedKeys = SearchLogic.getStoredCategorizedKeys();

            // Count total keys
            AI_SERVICES.forEach(service => {
                const serviceKeys = categorizedKeys[service.name] || {};
                totalKeys += Object.keys(serviceKeys).length;
            });

            if (totalKeys === 0) {
                alert("没有找到已存储的API密钥需要测试。");
                return;
            }

            debugLog(`开始测试 ${totalKeys} 个API密钥的状态`, "general");

            // Test keys for each service
            for (const service of AI_SERVICES) {
                const serviceKeys = categorizedKeys[service.name] || {};
                const keyList = Object.keys(serviceKeys);

                if (keyList.length === 0) continue;

                UIUpdaters.updateProgressDisplay(`正在测试 ${service.name} 的 ${keyList.length} 个密钥...`);
                debugLog(`测试 ${service.name} 的 ${keyList.length} 个密钥`, "general");

                try {
                    // Test keys one by one and update storage immediately
                    let serviceUpdatedCount = 0;
                    const storageKey = `${service.name.charAt(0).toLowerCase()}${service.name.slice(1)}ApiKeys`;

                    for (const key of keyList) {
                        try {
                            // Test single key
                            const statusResult = await ApiStatusChecker.checkApiKeyStatus(key, service.name as ApiServiceName);
                            const oldStatus = serviceKeys[key];
                            const newStatus = statusResult ? statusResult.statusCode : 429;

                            // Update status immediately if changed
                            if (oldStatus !== newStatus) {
                                serviceKeys[key] = newStatus;
                                serviceUpdatedCount++;
                                updatedKeys++;

                                // Save to storage immediately after each key test
                                GM_setValue(storageKey, serviceKeys);
                                debugLog(`立即更新 ${service.name} 密钥 ${key}: ${oldStatus} -> ${newStatus}`, "general");
                            }

                            testedKeys++;

                            // Update progress after each key
                            const progress = Math.round((testedKeys / totalKeys) * 100);
                            UIUpdaters.updateProgressDisplay(`测试进度: ${testedKeys}/${totalKeys} (${progress}%) - 当前: ${service.name}`);

                            // Small delay to avoid overwhelming the APIs
                            await new Promise(resolve => setTimeout(resolve, 500));

                        } catch (error) {
                            debugLog(`测试单个密钥 ${key} 时出错: ${error}`, "error");
                            testedKeys++;
                        }
                    }

                    debugLog(`${service.name}: 测试了 ${keyList.length} 个密钥，更新了 ${serviceUpdatedCount} 个状态`, "general");

                } catch (error) {
                    debugLog(`测试 ${service.name} 密钥时出错: ${error}`, "error");
                    // Continue with next service even if this one fails
                    testedKeys += keyList.length;
                }
            }

            // Update UI with new data
            UIUpdaters.updateKeyDisplay();

            const finalMessage = `测试完成！共测试 ${testedKeys} 个密钥，更新了 ${updatedKeys} 个状态。`;
            UIUpdaters.updateProgressDisplay(finalMessage);
            if (UIElements.statusElement) {
                UIElements.statusElement.textContent = finalMessage;
            }

            alert(finalMessage);
            debugLog(finalMessage, "general");

        } catch (error) {
            const errorMessage = `测试密钥状态时发生错误: ${error}`;
            debugLog(errorMessage, "error");
            UIUpdaters.updateProgressDisplay(errorMessage);
            if (UIElements.statusElement) {
                UIElements.statusElement.textContent = errorMessage;
            }
            alert(errorMessage);
        } finally {
            // Re-enable the test button
            if (UIElements.testKeysButtonElement) {
                UIElements.testKeysButtonElement.textContent = "测试密钥";
                (UIElements.testKeysButtonElement as HTMLButtonElement).disabled = false;
            }
        }
    }

    /**
     * Clears all stored AI service API keys (long-term and newly found) from storage.
     *
     * This method prompts the user for confirmation before clearing all stored
     * API keys including both long-term storage and newly found keys from the
     * current session. It also clears the in-memory session keys and updates
     * the UI to reflect the changes.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const clearButton = document.createElement('button');
     * clearButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleClearKeys);
     * ```
     */
    static handleClearKeys = (): void => {
        if (
            confirm(
                "确定要清空所有已存储的 AI 服务 API 密钥和本次新发现的密钥吗？此操作不可撤销。"
            )
        ) {
            // Clear only the AI service categories (long-term storage)
            const categoriesToClear: string[] = [];
            AI_SERVICES.forEach((service: { name: string }) => {
                categoriesToClear.push(
                    `${service.name.charAt(0).toLowerCase()}${service.name.slice(
                        1
                    )}ApiKeys`
                );
            });
            [...new Set(categoriesToClear)].forEach((gmKey: string) =>
                GM_setValue(gmKey, {})
            );
            debugLog("[+] 清空了所有长期存储的 AI 服务密钥。", "general");

            // Clear the newly found keys storage
            GM_setValue("newlyFoundApiKeys", {});
            debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");

            SearchLogic.clearCurrentSessionKeys(); // Also clear in-memory cumulative set

            UIUpdaters.updateKeyDisplay(); // Update UI to reflect cleared keys
            alert("所有已存储的 AI 服务 API 密钥和本次新发现的密钥已被清空。");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "所有密钥已清空。";
        }
    }

    /**
     * Copies debug information to the clipboard for troubleshooting purposes.
     *
     * This method collects all debug messages from various categories and
     * formats them into a readable text format for copying to the clipboard.
     * It provides user feedback about the operation's success or failure.
     *
     * @static
     * @returns void
     *
     * @example
     * ```typescript
     * // Used as button click handler
     * const debugButton = document.createElement('button');
     * debugButton.addEventListener('click', GitHubKeyFinderUIHandlers.handleCopyDebugInfo);
     * ```
     */
    static handleCopyDebugInfo = (): void => {
        let clipboardContent: string = "--- 调试信息 ---\n";
        clipboardContent += "------------------------------------\n";
        let hasMessages: boolean = false;

        for (const categoryKey in (SearchLogic as any).debugMessages) {
            const category: any = (SearchLogic as any).debugMessages[categoryKey];
            if (categoryKey === "jsonExtract") {
                if (category.messages) {
                    clipboardContent += `${category.title}:\n${category.messages}\n\n`;
                    hasMessages = true;
                }
            } else if (category.messages && category.messages.length > 0) {
                clipboardContent +=
                    `${category.title}:\n` + category.messages.join("\n") + "\n\n";
                hasMessages = true;
            }
        }

        if (!hasMessages) {
            clipboardContent += "暂无特定调试信息。点击“开始/重新搜索”以生成日志。";
        }

        if (hasMessages) {
            // Only copy if there's content
            GM_setClipboard(clipboardContent.trim(), "text");
            alert("调试信息已复制到剪贴板！");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "调试信息已复制。";
        } else {
            alert("没有调试信息可供复制。");
            if (UIElements.statusElement) UIElements.statusElement.textContent = "没有调试信息可供复制。";
        }
    }
}

/**
 * Export alias for backward compatibility.
 *
 * This provides a shorter alias for the GitHubKeyFinderUIHandlers class
 * to maintain compatibility with existing code that may use the shorter name.
 *
 * @example
 * ```typescript
 * // Both of these work the same way
 * UIHandlers.handleRunSearch();
 * GitHubKeyFinderUIHandlers.handleRunSearch();
 * ```
 */
export const UIHandlers = GitHubKeyFinderUIHandlers;