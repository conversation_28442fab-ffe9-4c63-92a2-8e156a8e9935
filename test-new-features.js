/**
 * 测试新功能：状态判断逻辑、复制格式、测试密钥功能
 */

// 模拟GM函数
const mockStorage = {};
let clipboardContent = "";

function GM_getValue(key, defaultValue) {
    return mockStorage[key] !== undefined ? mockStorage[key] : defaultValue;
}

function GM_setValue(key, value) {
    mockStorage[key] = value;
    console.log(`存储 ${key}:`, value);
}

function GM_setClipboard(content, type) {
    clipboardContent = content;
    console.log(`复制到剪贴板 (${type}):`, content);
}

// 测试状态判断逻辑
console.log("=== 测试状态判断逻辑 ===");

function getStatusText(statusCode) {
    if ((statusCode >= 200 && statusCode < 300) || statusCode === 429) {
        return "有效";
    } else if (statusCode === 401 || statusCode === 403) {
        return "无效";
    } else {
        return `状态${statusCode}`;
    }
}

const testStatusCodes = [200, 201, 299, 300, 401, 403, 429, 404, 500];
console.log("状态码映射测试:");
testStatusCodes.forEach(code => {
    const result = getStatusText(code);
    console.log(`${code} -> ${result}`);
});

// 验证429现在被认为是有效的
console.log("\n验证429状态码:");
console.log(`429 -> ${getStatusText(429)} (应该是'有效')`);

// 测试新的复制格式
console.log("\n=== 测试新的复制格式 ===");

const testKeys = {
    "sk-test200": 200,
    "sk-test429": 429,
    "sk-test401": 401,
    "AIzaSy_test200": 200,
    "claude-test403": 403,
    "grok-test500": 500
};

GM_setValue("newlyFoundApiKeys", testKeys);

function handleCopyKeys() {
    const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", {});
    const keyCount = Object.keys(newlyFoundKeys).length;

    if (keyCount > 0) {
        // Format keys with status codes in key:statuscode format
        const keyLines = [];
        Object.entries(newlyFoundKeys).forEach(([key, statusCode]) => {
            keyLines.push(`${key}:${statusCode}`);
        });

        const clipboardContent = keyLines.join("\n");
        GM_setClipboard(clipboardContent.trim(), "text");
        console.log(`✅ ${keyCount} 个密钥（key:statuscode格式）已复制到剪贴板！`);
        return true;
    } else {
        console.log("❌ 没有密钥可供复制。");
        return false;
    }
}

const copyResult = handleCopyKeys();

console.log("\n复制结果验证:");
console.log("剪贴板内容:");
console.log(clipboardContent);

// 验证格式
const lines = clipboardContent.split('\n');
console.log(`\n格式验证 - 总行数: ${lines.length}`);
lines.forEach((line, index) => {
    const parts = line.split(':');
    if (parts.length === 2) {
        const [key, statusCode] = parts;
        console.log(`✅ 第${index + 1}行: ${key} -> ${statusCode}`);
    } else {
        console.log(`❌ 第${index + 1}行格式错误: ${line}`);
    }
});

// 测试状态统计功能
console.log("\n=== 测试状态统计功能 ===");

function getStatusCounts(keys) {
    const statusCounts = {};
    Object.values(keys).forEach(statusCode => {
        statusCounts[statusCode] = (statusCounts[statusCode] || 0) + 1;
    });
    return statusCounts;
}

function formatStatusDisplay(statusCounts) {
    const statusParts = [];
    Object.entries(statusCounts).forEach(([status, count]) => {
        const statusCode = parseInt(status);
        let statusText = "";
        if ((statusCode >= 200 && statusCode < 300) || statusCode === 429) {
            statusText = "有效";
        } else if (statusCode === 401 || statusCode === 403) {
            statusText = "无效";
        } else {
            statusText = `状态${statusCode}`;
        }
        statusParts.push(`${statusText}: ${count}`);
    });
    return statusParts.join(", ");
}

const statusCounts = getStatusCounts(testKeys);
console.log("状态统计:", statusCounts);
console.log("格式化显示:", formatStatusDisplay(statusCounts));

// 模拟测试密钥功能
console.log("\n=== 模拟测试密钥功能 ===");

// 设置一些存储的密钥用于测试
const storedKeys = {
    "OpenAI": {
        "sk-old1": 200,
        "sk-old2": 429,
        "sk-old3": 401
    },
    "Gemini": {
        "AIzaSy_old1": 200,
        "AIzaSy_old2": 403
    }
};

// 模拟存储
Object.entries(storedKeys).forEach(([serviceName, keys]) => {
    const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
    GM_setValue(storageKey, keys);
});

// 模拟API状态检查器
class MockApiStatusChecker {
    static async checkMultipleApiKeys(keys, serviceName) {
        console.log(`模拟检查 ${serviceName} 的 ${keys.length} 个密钥...`);
        
        const results = new Map();
        keys.forEach(key => {
            // 模拟随机状态变化
            const randomStatuses = [200, 401, 429, 403];
            const randomStatus = randomStatuses[Math.floor(Math.random() * randomStatuses.length)];
            results.set(key, { statusCode: randomStatus });
        });
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 100));
        return results;
    }
}

async function simulateTestKeys() {
    console.log("开始模拟测试密钥功能...");
    
    let totalKeys = 0;
    let testedKeys = 0;
    let updatedKeys = 0;
    
    const services = ["OpenAI", "Gemini"];
    
    // 计算总密钥数
    services.forEach(serviceName => {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const serviceKeys = GM_getValue(storageKey, {});
        totalKeys += Object.keys(serviceKeys).length;
    });
    
    console.log(`总共需要测试 ${totalKeys} 个密钥`);
    
    // 测试每个服务的密钥
    for (const serviceName of services) {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const serviceKeys = GM_getValue(storageKey, {});
        const keyList = Object.keys(serviceKeys);
        
        if (keyList.length === 0) continue;
        
        console.log(`正在测试 ${serviceName} 的 ${keyList.length} 个密钥...`);
        
        try {
            const statusResults = await MockApiStatusChecker.checkMultipleApiKeys(keyList, serviceName);
            
            let serviceUpdatedCount = 0;
            keyList.forEach(key => {
                const oldStatus = serviceKeys[key];
                const statusResult = statusResults.get(key);
                const newStatus = statusResult ? statusResult.statusCode : 429;
                
                console.log(`  ${key}: ${oldStatus} -> ${newStatus}`);
                
                if (oldStatus !== newStatus) {
                    serviceKeys[key] = newStatus;
                    serviceUpdatedCount++;
                    updatedKeys++;
                }
                testedKeys++;
            });
            
            // 保存更新的密钥
            GM_setValue(storageKey, serviceKeys);
            console.log(`${serviceName}: 测试了 ${keyList.length} 个密钥，更新了 ${serviceUpdatedCount} 个状态`);
            
        } catch (error) {
            console.log(`测试 ${serviceName} 密钥时出错: ${error}`);
            testedKeys += keyList.length;
        }
        
        const progress = Math.round((testedKeys / totalKeys) * 100);
        console.log(`测试进度: ${testedKeys}/${totalKeys} (${progress}%)`);
    }
    
    const finalMessage = `测试完成！共测试 ${testedKeys} 个密钥，更新了 ${updatedKeys} 个状态。`;
    console.log(finalMessage);
    
    return { testedKeys, updatedKeys };
}

// 执行模拟测试
simulateTestKeys().then(result => {
    console.log("\n=== 测试结果 ===");
    console.log(`✅ 状态判断逻辑更新完成 (429现在被认为是有效)`);
    console.log(`✅ 复制格式更新完成 (key:statuscode格式)`);
    console.log(`✅ 测试密钥功能模拟完成 (测试了${result.testedKeys}个密钥，更新了${result.updatedKeys}个状态)`);
    console.log(`✅ 数据迁移代码已删除`);
    console.log("\n所有新功能测试通过！");
});
