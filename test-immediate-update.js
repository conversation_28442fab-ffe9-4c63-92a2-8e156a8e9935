/**
 * 测试立即更新存储的功能
 * 验证测试密钥时是否每测试一个就立即更新存储
 */

// 模拟GM函数
const mockStorage = {};
const storageHistory = []; // 记录存储操作历史

function GM_getValue(key, defaultValue) {
    return mockStorage[key] !== undefined ? mockStorage[key] : defaultValue;
}

function GM_setValue(key, value) {
    mockStorage[key] = value;
    const timestamp = new Date().toISOString();
    storageHistory.push({
        timestamp,
        key,
        value: JSON.parse(JSON.stringify(value)) // 深拷贝
    });
    console.log(`[${timestamp}] 存储更新 ${key}:`, value);
}

// 模拟API状态检查器
class MockApiStatusChecker {
    static async checkApiKeyStatus(key, serviceName) {
        console.log(`正在检查 ${serviceName} 密钥: ${key}`);
        
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 模拟随机状态变化
        const randomStatuses = [200, 401, 429, 403];
        const randomStatus = randomStatuses[Math.floor(Math.random() * randomStatuses.length)];
        
        console.log(`  检查结果: ${key} -> ${randomStatus}`);
        return { statusCode: randomStatus };
    }
}

// 模拟UI更新器
class MockUIUpdaters {
    static updateProgressDisplay(message) {
        console.log(`[进度] ${message}`);
    }
    
    static updateKeyDisplay() {
        console.log(`[UI] 更新密钥显示`);
    }
}

// 设置测试数据
console.log("=== 设置测试数据 ===");

const testData = {
    "OpenAI": {
        "sk-test1": 200,
        "sk-test2": 429,
        "sk-test3": 401
    },
    "Gemini": {
        "AIzaSy_test1": 200,
        "AIzaSy_test2": 403
    }
};

// 初始化存储
Object.entries(testData).forEach(([serviceName, keys]) => {
    const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
    GM_setValue(storageKey, keys);
});

console.log("初始数据设置完成");
console.log("存储历史记录数:", storageHistory.length);

// 清空历史记录，开始测试
storageHistory.length = 0;

// 模拟测试密钥功能（简化版）
async function simulateTestKeysWithImmediateUpdate() {
    console.log("\n=== 开始模拟立即更新测试 ===");
    
    const services = ["OpenAI", "Gemini"];
    let totalKeys = 0;
    let testedKeys = 0;
    let updatedKeys = 0;
    
    // 计算总密钥数
    services.forEach(serviceName => {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const serviceKeys = GM_getValue(storageKey, {});
        totalKeys += Object.keys(serviceKeys).length;
    });
    
    console.log(`总共需要测试 ${totalKeys} 个密钥`);
    
    // 测试每个服务的密钥
    for (const serviceName of services) {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const serviceKeys = GM_getValue(storageKey, {});
        const keyList = Object.keys(serviceKeys);
        
        if (keyList.length === 0) continue;
        
        console.log(`\n正在测试 ${serviceName} 的 ${keyList.length} 个密钥...`);
        
        try {
            let serviceUpdatedCount = 0;
            
            // 逐个测试密钥并立即更新存储
            for (const key of keyList) {
                try {
                    console.log(`\n--- 测试密钥 ${key} ---`);
                    
                    // 记录测试前的存储状态
                    const beforeTest = JSON.parse(JSON.stringify(serviceKeys));
                    console.log(`测试前状态: ${key} = ${beforeTest[key]}`);
                    
                    // 测试单个密钥
                    const statusResult = await MockApiStatusChecker.checkApiKeyStatus(key, serviceName);
                    const oldStatus = serviceKeys[key];
                    const newStatus = statusResult ? statusResult.statusCode : 429;
                    
                    // 立即更新状态如果有变化
                    if (oldStatus !== newStatus) {
                        serviceKeys[key] = newStatus;
                        serviceUpdatedCount++;
                        updatedKeys++;
                        
                        // 立即保存到存储
                        GM_setValue(storageKey, serviceKeys);
                        console.log(`✅ 立即更新 ${serviceName} 密钥 ${key}: ${oldStatus} -> ${newStatus}`);
                        
                        // 验证存储确实被更新了
                        const verifyStorage = GM_getValue(storageKey, {});
                        console.log(`验证存储: ${key} = ${verifyStorage[key]} (应该是 ${newStatus})`);
                        
                    } else {
                        console.log(`⏭️  密钥 ${key} 状态未变化: ${oldStatus}`);
                    }
                    
                    testedKeys++;
                    
                    // 更新进度
                    const progress = Math.round((testedKeys / totalKeys) * 100);
                    MockUIUpdaters.updateProgressDisplay(`测试进度: ${testedKeys}/${totalKeys} (${progress}%) - 当前: ${serviceName}`);
                    
                    // 小延迟避免过快
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                } catch (error) {
                    console.log(`❌ 测试单个密钥 ${key} 时出错: ${error}`);
                    testedKeys++;
                }
            }
            
            console.log(`${serviceName}: 测试了 ${keyList.length} 个密钥，更新了 ${serviceUpdatedCount} 个状态`);
            
        } catch (error) {
            console.log(`测试 ${serviceName} 密钥时出错: ${error}`);
            testedKeys += keyList.length;
        }
    }
    
    // 更新UI
    MockUIUpdaters.updateKeyDisplay();
    
    const finalMessage = `测试完成！共测试 ${testedKeys} 个密钥，更新了 ${updatedKeys} 个状态。`;
    console.log(`\n${finalMessage}`);
    
    return { testedKeys, updatedKeys };
}

// 执行测试
simulateTestKeysWithImmediateUpdate().then(result => {
    console.log("\n=== 存储操作历史分析 ===");
    console.log(`总存储操作次数: ${storageHistory.length}`);
    
    // 分析存储操作
    const operationsByService = {};
    storageHistory.forEach((op, index) => {
        console.log(`${index + 1}. [${op.timestamp}] ${op.key}`);
        
        // 统计每个服务的操作次数
        const serviceName = op.key.replace('ApiKeys', '');
        operationsByService[serviceName] = (operationsByService[serviceName] || 0) + 1;
        
        // 显示密钥状态变化
        const keys = Object.keys(op.value);
        console.log(`   密钥数量: ${keys.length}`);
        keys.forEach(key => {
            console.log(`   ${key}: ${op.value[key]}`);
        });
    });
    
    console.log("\n每个服务的存储操作次数:");
    Object.entries(operationsByService).forEach(([service, count]) => {
        console.log(`${service}: ${count} 次操作`);
    });
    
    console.log("\n=== 验证结果 ===");
    
    // 验证是否每个密钥测试后都有存储操作
    const expectedOperations = result.updatedKeys; // 只有状态变化的密钥才会触发存储
    const actualOperations = storageHistory.length - 2; // 减去初始设置的2次操作
    
    console.log(`期望的存储操作次数: ${expectedOperations} (只有状态变化的密钥)`);
    console.log(`实际的存储操作次数: ${actualOperations}`);
    
    if (actualOperations > 0) {
        console.log("✅ 确认：每个状态变化的密钥都立即触发了存储更新");
        console.log("✅ 立即更新功能工作正常！");
    } else {
        console.log("⚠️  没有检测到状态变化，可能所有密钥状态都没有改变");
    }
    
    console.log("\n=== 最终存储状态 ===");
    Object.keys(testData).forEach(serviceName => {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const finalState = GM_getValue(storageKey, {});
        console.log(`${serviceName}:`, finalState);
    });
});
