# 代码优化建议

基于新的字典格式数据结构，以下是进一步优化代码逻辑的建议：

## 1. 密钥管理优化

### 1.1 智能状态更新
```typescript
/**
 * 定期更新密钥状态，避免使用过期的无效密钥
 */
class KeyStatusManager {
    private static readonly STATUS_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时
    
    static async refreshExpiredStatuses(): Promise<void> {
        const services = AI_SERVICES;
        for (const service of services) {
            const keys = this.getKeysNeedingRefresh(service.name);
            if (keys.length > 0) {
                await this.updateKeyStatuses(keys, service.name);
            }
        }
    }
    
    private static getKeysNeedingRefresh(serviceName: string): string[] {
        // 获取状态为429或超过24小时未检查的密钥
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const keys = GM_getValue(storageKey, {});
        const lastCheckKey = `${serviceName.toLowerCase()}LastCheck`;
        const lastCheck = GM_getValue(lastCheckKey, {});
        
        return Object.keys(keys).filter(key => {
            const status = keys[key];
            const lastCheckTime = lastCheck[key] || 0;
            const isExpired = Date.now() - lastCheckTime > this.STATUS_CACHE_DURATION;
            
            return status === 429 || isExpired;
        });
    }
}
```

### 1.2 密钥优先级系统
```typescript
/**
 * 根据状态码为密钥分配优先级
 */
class KeyPriorityManager {
    static getKeysByPriority(serviceName: string): string[] {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const keys = GM_getValue(storageKey, {});
        
        return Object.entries(keys)
            .sort(([, statusA], [, statusB]) => {
                return this.getStatusPriority(statusA) - this.getStatusPriority(statusB);
            })
            .map(([key]) => key);
    }
    
    private static getStatusPriority(status: number): number {
        if (status >= 200 && status < 300) return 1; // 有效密钥优先
        if (status === 429) return 2; // 未知状态次之
        return 3; // 无效密钥最后
    }
}
```

## 2. 搜索逻辑优化

### 2.1 智能搜索策略
```typescript
/**
 * 基于已有密钥状态优化搜索策略
 */
class SmartSearchStrategy {
    static shouldSkipService(serviceName: string): boolean {
        const validKeys = this.getValidKeysCount(serviceName);
        const totalKeys = this.getTotalKeysCount(serviceName);
        
        // 如果已有足够的有效密钥，可以降低搜索优先级
        if (validKeys >= 5) {
            return Math.random() > 0.3; // 70%概率跳过
        }
        
        return false;
    }
    
    static getSearchPriority(serviceName: string): number {
        const validKeys = this.getValidKeysCount(serviceName);
        const invalidKeys = this.getInvalidKeysCount(serviceName);
        
        // 有效密钥少的服务优先搜索
        return 100 - validKeys * 10 + invalidKeys * 2;
    }
}
```

### 2.2 重复密钥检测优化
```typescript
/**
 * 更高效的重复密钥检测
 */
class DuplicateKeyDetector {
    private static keyHashCache = new Map<string, Set<string>>();
    
    static isDuplicate(key: string, serviceName: string): boolean {
        if (!this.keyHashCache.has(serviceName)) {
            this.buildHashCache(serviceName);
        }
        
        return this.keyHashCache.get(serviceName)?.has(key) || false;
    }
    
    private static buildHashCache(serviceName: string): void {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const keys = GM_getValue(storageKey, {});
        const keySet = new Set(Object.keys(keys));
        this.keyHashCache.set(serviceName, keySet);
    }
}
```

## 3. UI优化建议

### 3.1 状态可视化增强
```typescript
/**
 * 更丰富的状态显示
 */
class StatusVisualizer {
    static createStatusBadge(statusCode: number): HTMLElement {
        const badge = document.createElement('span');
        badge.className = `status-badge status-${this.getStatusClass(statusCode)}`;
        badge.textContent = this.getStatusText(statusCode);
        badge.title = `HTTP状态码: ${statusCode}`;
        return badge;
    }
    
    static createStatusChart(serviceName: string): HTMLElement {
        const statusCounts = this.getStatusCounts(serviceName);
        const chart = document.createElement('div');
        chart.className = 'status-chart';
        
        // 创建简单的条形图显示状态分布
        Object.entries(statusCounts).forEach(([status, count]) => {
            const bar = document.createElement('div');
            bar.className = `status-bar status-${this.getStatusClass(parseInt(status))}`;
            bar.style.width = `${(count / this.getTotalCount(statusCounts)) * 100}%`;
            bar.title = `${this.getStatusText(parseInt(status))}: ${count}`;
            chart.appendChild(bar);
        });
        
        return chart;
    }
}
```

### 3.2 密钥操作增强
```typescript
/**
 * 密钥管理操作
 */
class KeyOperations {
    static async refreshKeyStatus(key: string, serviceName: string): Promise<void> {
        const result = await ApiStatusChecker.checkApiKeyStatus(key, serviceName);
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const keys = GM_getValue(storageKey, {});
        keys[key] = result.statusCode;
        GM_setValue(storageKey, keys);
        
        // 更新UI
        UI.updateKeyDisplay();
    }
    
    static removeInvalidKeys(serviceName: string): void {
        const storageKey = `${serviceName.toLowerCase()}ApiKeys`;
        const keys = GM_getValue(storageKey, {});
        const validKeys = {};
        
        Object.entries(keys).forEach(([key, status]) => {
            if (status >= 200 && status < 300) {
                validKeys[key] = status;
            }
        });
        
        GM_setValue(storageKey, validKeys);
        UI.updateKeyDisplay();
    }
    
    static exportKeys(serviceName?: string): string {
        const services = serviceName ? [serviceName] : AI_SERVICES.map(s => s.name);
        const exportData = {};
        
        services.forEach(service => {
            const storageKey = `${service.toLowerCase()}ApiKeys`;
            exportData[service] = GM_getValue(storageKey, {});
        });
        
        return JSON.stringify(exportData, null, 2);
    }
}
```

## 4. 性能优化

### 4.1 缓存机制
```typescript
/**
 * 状态检查结果缓存
 */
class StatusCache {
    private static cache = new Map<string, {status: number, timestamp: number}>();
    private static readonly CACHE_DURATION = 30 * 60 * 1000; // 30分钟
    
    static getCachedStatus(key: string): number | null {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            return cached.status;
        }
        return null;
    }
    
    static setCachedStatus(key: string, status: number): void {
        this.cache.set(key, {status, timestamp: Date.now()});
    }
}
```

### 4.2 批量操作优化
```typescript
/**
 * 优化批量状态检查
 */
class BatchProcessor {
    static async processBatch<T>(
        items: T[], 
        processor: (item: T) => Promise<any>,
        batchSize: number = 3,
        delay: number = 1000
    ): Promise<any[]> {
        const results = [];
        
        for (let i = 0; i < items.length; i += batchSize) {
            const batch = items.slice(i, i + batchSize);
            const batchResults = await Promise.allSettled(
                batch.map(processor)
            );
            
            results.push(...batchResults);
            
            // 批次间延迟
            if (i + batchSize < items.length) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        return results;
    }
}
```

## 5. 错误处理优化

### 5.1 重试机制
```typescript
/**
 * 智能重试机制
 */
class RetryManager {
    static async withRetry<T>(
        operation: () => Promise<T>,
        maxRetries: number = 3,
        backoffMs: number = 1000
    ): Promise<T> {
        let lastError: Error;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error as Error;
                
                if (attempt < maxRetries) {
                    const delay = backoffMs * Math.pow(2, attempt);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        
        throw lastError!;
    }
}
```

## 6. 配置管理

### 6.1 动态配置
```typescript
/**
 * 可配置的参数管理
 */
class ConfigManager {
    private static readonly CONFIG_KEY = 'apiKeyFinderConfig';
    
    static getConfig() {
        return GM_getValue(this.CONFIG_KEY, {
            statusCheckInterval: 24 * 60 * 60 * 1000, // 24小时
            batchSize: 3,
            requestDelay: 1000,
            maxRetries: 3,
            enableAutoRefresh: true,
            prioritizeValidKeys: true
        });
    }
    
    static updateConfig(updates: Partial<any>): void {
        const current = this.getConfig();
        GM_setValue(this.CONFIG_KEY, {...current, ...updates});
    }
}
```

这些优化建议可以进一步提升系统的性能、用户体验和可维护性。建议按优先级逐步实施。
