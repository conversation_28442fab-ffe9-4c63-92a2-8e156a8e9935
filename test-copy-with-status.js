/**
 * 测试带状态码的密钥复制功能
 * 验证newlyFoundApiKeys的新字典格式和复制功能
 */

// 模拟GM_getValue和GM_setClipboard函数
const mockStorage = {};
let clipboardContent = "";

function GM_getValue(key, defaultValue) {
    return mockStorage[key] !== undefined ? mockStorage[key] : defaultValue;
}

function GM_setValue(key, value) {
    mockStorage[key] = value;
    console.log(`存储 ${key}:`, value);
}

function GM_setClipboard(content, type) {
    clipboardContent = content;
    console.log(`复制到剪贴板 (${type}):`, content);
}

// 模拟新格式的newlyFoundApiKeys数据
console.log("=== 设置测试数据 ===");

const testNewlyFoundKeys = {
    "sk-test1234567890abcdef": 200,  // 有效密钥
    "sk-invalid123456789": 401,     // 无效密钥
    "AIzaSy_test123456789": 200,    // 有效Gemini密钥
    "AIzaSy_unknown123456": 429,    // 未知状态密钥
    "sk-forbidden123456": 403,      // 禁止访问密钥
    "claude-test123456": 500        // 服务器错误密钥
};

GM_setValue("newlyFoundApiKeys", testNewlyFoundKeys);

console.log("测试数据设置完成");

// 模拟复制功能
function handleCopyKeys() {
    const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", {});
    const keyCount = Object.keys(newlyFoundKeys).length;

    if (keyCount > 0) {
        // Format keys with status codes
        const keyLines = [];
        Object.entries(newlyFoundKeys).forEach(([key, statusCode]) => {
            let statusText = "";
            if (statusCode >= 200 && statusCode < 300) {
                statusText = "有效";
            } else if (statusCode === 401 || statusCode === 403) {
                statusText = "无效";
            } else if (statusCode === 429) {
                statusText = "未知";
            } else {
                statusText = `状态${statusCode}`;
            }
            keyLines.push(`${key} (${statusText})`);
        });

        const clipboardContent = keyLines.join("\n");
        GM_setClipboard(clipboardContent.trim(), "text");
        console.log(`✅ ${keyCount} 个本次新发现的密钥（含状态码）已复制到剪贴板！`);
        return true;
    } else {
        console.log("❌ 本次运行没有发现新的密钥可供复制。");
        return false;
    }
}

// 测试复制功能
console.log("\n=== 测试复制功能 ===");
const copyResult = handleCopyKeys();

console.log("\n=== 复制结果验证 ===");
console.log("剪贴板内容:");
console.log(clipboardContent);

// 验证复制内容格式
console.log("\n=== 格式验证 ===");
const lines = clipboardContent.split('\n');
console.log(`总行数: ${lines.length}`);

lines.forEach((line, index) => {
    console.log(`第${index + 1}行: ${line}`);
    
    // 验证格式是否正确 (密钥 (状态))
    const match = line.match(/^(.+) \((.+)\)$/);
    if (match) {
        const [, key, status] = match;
        console.log(`  ✅ 密钥: ${key}, 状态: ${status}`);
    } else {
        console.log(`  ❌ 格式错误: ${line}`);
    }
});

// 测试状态码映射
console.log("\n=== 状态码映射测试 ===");
const statusMappingTests = [
    { code: 200, expected: "有效" },
    { code: 201, expected: "有效" },
    { code: 299, expected: "有效" },
    { code: 401, expected: "无效" },
    { code: 403, expected: "无效" },
    { code: 429, expected: "未知" },
    { code: 500, expected: "状态500" },
    { code: 404, expected: "状态404" }
];

function getStatusText(statusCode) {
    if (statusCode >= 200 && statusCode < 300) {
        return "有效";
    } else if (statusCode === 401 || statusCode === 403) {
        return "无效";
    } else if (statusCode === 429) {
        return "未知";
    } else {
        return `状态${statusCode}`;
    }
}

statusMappingTests.forEach(test => {
    const result = getStatusText(test.code);
    const isCorrect = result === test.expected;
    console.log(`状态码 ${test.code}: ${result} ${isCorrect ? '✅' : '❌'} (期望: ${test.expected})`);
});

// 测试空数据情况
console.log("\n=== 空数据测试 ===");
GM_setValue("newlyFoundApiKeys", {});
console.log("设置空的newlyFoundApiKeys");

function handleCopyKeysEmpty() {
    const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", {});
    const keyCount = Object.keys(newlyFoundKeys).length;

    if (keyCount > 0) {
        console.log(`✅ 找到 ${keyCount} 个密钥`);
        return true;
    } else {
        console.log("❌ 本次运行没有发现新的密钥可供复制。");
        return false;
    }
}

const emptyResult = handleCopyKeysEmpty();
console.log(`空数据测试结果: ${emptyResult ? '失败' : '成功'}`);

// 测试数据迁移场景
console.log("\n=== 数据迁移测试 ===");

// 模拟旧格式数据
const oldFormatKeys = ["sk-old1", "sk-old2", "AIzaSy_old1"];
GM_setValue("newlyFoundApiKeys", oldFormatKeys);
console.log("设置旧格式数据:", oldFormatKeys);

// 检测并迁移
function migrateNewlyFoundKeys() {
    const oldNewlyFound = GM_getValue("newlyFoundApiKeys", []);
    
    if (Array.isArray(oldNewlyFound)) {
        console.log(`检测到旧格式，迁移 ${oldNewlyFound.length} 个密钥`);
        
        const newFormat = {};
        oldNewlyFound.forEach((key) => {
            newFormat[key] = 429; // 默认未知状态
        });
        
        GM_setValue("newlyFoundApiKeys", newFormat);
        console.log("迁移完成:", newFormat);
        return true;
    } else {
        console.log("已经是新格式，无需迁移");
        return false;
    }
}

const migrationResult = migrateNewlyFoundKeys();
console.log(`迁移结果: ${migrationResult ? '成功' : '跳过'}`);

// 验证迁移后的复制功能
console.log("\n=== 迁移后复制测试 ===");
handleCopyKeys();

console.log("\n=== 测试完成 ===");
console.log("✅ newlyFoundApiKeys字典格式和带状态码复制功能测试通过！");
console.log("✅ 数据迁移功能测试通过！");
console.log("✅ 状态码映射功能测试通过！");
