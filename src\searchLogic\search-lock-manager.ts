/**
 * Search Lock Manager Module
 *
 * This module manages cross-page search locking to ensure only one page
 * can perform API key searches at a time. It prevents data conflicts and
 * ensures consistent state management across multiple browser tabs/windows.
 *
 * @module SearchLockManager
 */

import { GM_setValue, GM_getValue } from '$';
import { debugLog } from "../debuglog";
import type { ISearchLockState } from "../types";

/**
 * Manages search locking across multiple pages/tabs to prevent concurrent searches
 * and data conflicts. Uses GM storage for cross-page communication.
 *
 * @class SearchLockManager
 *
 * @example
 * ```typescript
 * const lockManager = new SearchLockManager();
 *
 * // Try to acquire lock before starting search
 * if (lockManager.acquireLock()) {
 *     // Start search
 *     await performSearch();
 *     lockManager.releaseLock();
 * } else {
 *     console.log("Another page is already searching");
 * }
 * ```
 */
export class SearchLockManager {
    private readonly LOCK_STORAGE_KEY = "searchLockState";
    private readonly LOCK_TIMEOUT_MS = 30 * 60 * 1000; // 30分钟超时
    private readonly pageId: string;

    /**
     * Creates a new SearchLockManager instance with a unique page identifier
     */
    constructor() {
        // 生成唯一的页面标识符
        this.pageId = `page_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        debugLog(`SearchLockManager 初始化，页面ID: ${this.pageId}`, "general");

        // 监听页面卸载事件，确保释放锁定
        this.setupPageUnloadHandler();
    }

    /**
     * Gets the current page identifier
     * @returns The unique page identifier for this instance
     */
    public getPageId(): string {
        return this.pageId;
    }

    /**
     * Attempts to acquire the search lock for this page
     * @returns true if lock was successfully acquired, false otherwise
     */
    public acquireLock(): boolean {
        const currentLock = this.getCurrentLockState();

        // 检查是否已经有锁定且未超时
        if (currentLock.isLocked && !this.isLockExpired(currentLock)) {
            if (currentLock.lockedBy === this.pageId) {
                // 当前页面已经持有锁定
                debugLog(`当前页面已持有搜索锁定`, "general");
                return true;
            } else {
                // 其他页面持有锁定
                debugLog(`搜索锁定被其他页面持有: ${currentLock.lockedBy}`, "general");
                return false;
            }
        }

        // 获取锁定
        const newLockState: ISearchLockState = {
            isLocked: true,
            lockedBy: this.pageId,
            lockTimestamp: Date.now(),
            searchInProgress: false
        };

        GM_setValue(this.LOCK_STORAGE_KEY, newLockState);
        debugLog(`成功获取搜索锁定，页面ID: ${this.pageId}`, "general");
        return true;
    }

    /**
     * Releases the search lock if it's held by this page
     * @returns true if lock was successfully released, false if not held by this page
     */
    public releaseLock(): boolean {
        const currentLock = this.getCurrentLockState();

        if (!currentLock.isLocked || currentLock.lockedBy !== this.pageId) {
            debugLog(`无法释放锁定：当前页面未持有锁定`, "general");
            return false;
        }

        const releasedLockState: ISearchLockState = {
            isLocked: false,
            lockedBy: "",
            lockTimestamp: 0,
            searchInProgress: false
        };

        GM_setValue(this.LOCK_STORAGE_KEY, releasedLockState);
        debugLog(`成功释放搜索锁定，页面ID: ${this.pageId}`, "general");
        return true;
    }

    /**
     * Updates the search progress status for the current lock holder
     * @param inProgress Whether search is currently in progress
     * @returns true if status was updated, false if this page doesn't hold the lock
     */
    public updateSearchProgress(inProgress: boolean): boolean {
        const currentLock = this.getCurrentLockState();

        if (!currentLock.isLocked || currentLock.lockedBy !== this.pageId) {
            debugLog(`无法更新搜索状态：当前页面未持有锁定`, "general");
            return false;
        }

        const updatedLockState: ISearchLockState = {
            ...currentLock,
            searchInProgress: inProgress
        };

        GM_setValue(this.LOCK_STORAGE_KEY, updatedLockState);
        debugLog(`更新搜索进度状态: ${inProgress}`, "general");
        return true;
    }

    /**
     * Checks if this page currently holds the search lock
     * @returns true if this page holds the lock, false otherwise
     */
    public hasLock(): boolean {
        const currentLock = this.getCurrentLockState();
        return currentLock.isLocked &&
            currentLock.lockedBy === this.pageId &&
            !this.isLockExpired(currentLock);
    }

    /**
     * Checks if any page currently holds the search lock
     * @returns true if any page holds the lock, false otherwise
     */
    public isLocked(): boolean {
        const currentLock = this.getCurrentLockState();
        return currentLock.isLocked && !this.isLockExpired(currentLock);
    }

    /**
     * Gets information about the current lock state
     * @returns Object containing lock information
     */
    public getLockInfo(): { isLocked: boolean; lockedBy: string; searchInProgress: boolean; isExpired: boolean } {
        const currentLock = this.getCurrentLockState();
        const isExpired = this.isLockExpired(currentLock);

        return {
            isLocked: currentLock.isLocked && !isExpired,
            lockedBy: currentLock.lockedBy,
            searchInProgress: currentLock.searchInProgress,
            isExpired: isExpired
        };
    }

    /**
     * Forces release of any existing lock (use with caution)
     * This should only be used for cleanup or emergency situations
     */
    public forceReleaseLock(): void {
        const releasedLockState: ISearchLockState = {
            isLocked: false,
            lockedBy: "",
            lockTimestamp: 0,
            searchInProgress: false
        };

        GM_setValue(this.LOCK_STORAGE_KEY, releasedLockState);
        debugLog(`强制释放搜索锁定`, "general");
    }

    /**
     * Gets the current lock state from storage
     * @private
     */
    private getCurrentLockState(): ISearchLockState {
        const defaultState: ISearchLockState = {
            isLocked: false,
            lockedBy: "",
            lockTimestamp: 0,
            searchInProgress: false
        };

        return GM_getValue(this.LOCK_STORAGE_KEY, defaultState) as ISearchLockState;
    }

    /**
     * Checks if the given lock state has expired
     * @private
     */
    private isLockExpired(lockState: ISearchLockState): boolean {
        if (!lockState.isLocked || lockState.lockTimestamp === 0) {
            return false;
        }

        const now = Date.now();
        const elapsed = now - lockState.lockTimestamp;
        const expired = elapsed > this.LOCK_TIMEOUT_MS;

        if (expired) {
            debugLog(`搜索锁定已超时，经过时间: ${Math.round(elapsed / 1000)}秒`, "general");
        }

        return expired;
    }

    /**
     * Sets up page unload handler to release lock when page is closed
     * @private
     */
    private setupPageUnloadHandler(): void {
        const handlePageUnload = () => {
            debugLog(`页面卸载，释放搜索锁定`, "general");
            this.releaseLock();
        };

        // 监听多个事件以确保锁定被释放
        window.addEventListener('beforeunload', handlePageUnload);
        window.addEventListener('unload', handlePageUnload);
        window.addEventListener('pagehide', handlePageUnload);

        // 对于某些浏览器，也监听visibilitychange
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                // 页面变为隐藏状态，可能是关闭或切换标签页
                // 这里不立即释放锁定，因为用户可能只是切换标签页
                debugLog(`页面变为隐藏状态`, "general");
            }
        });
    }
}

export default SearchLockManager;
